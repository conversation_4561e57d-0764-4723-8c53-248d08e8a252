<?php
require_once 'config.php';
requireLogin();

$user = getCurrentUser();
$success = '';
$error = '';

// Handle blog deletion
if (isset($_GET['delete'])) {
    $blog_id = filter_var($_GET['delete'], FILTER_VALIDATE_INT);
    if ($blog_id && $blog_id > 0) {
        // Only allow users to delete their own blogs
        $stmt = $pdo->prepare("DELETE FROM blogs WHERE id = ? AND author_id = ?");
        if ($stmt->execute([$blog_id, $user['id']])) {
            $success = 'Blog post deleted successfully!';
        } else {
            $error = 'Failed to delete blog post or you don\'t have permission.';
        }
    } else {
        $error = 'Invalid blog ID.';
    }
}

// Handle status toggle
if (isset($_GET['toggle']) && isset($_GET['status'])) {
    $blog_id = filter_var($_GET['toggle'], FILTER_VALIDATE_INT);
    $current_status = filter_var($_GET['status'], FILTER_SANITIZE_STRING);
    
    if ($blog_id && $blog_id > 0 && in_array($current_status, ['draft', 'published', 'pending'])) {
        $new_status = $current_status === 'draft' ? 'pending' : 'draft';
        
        $stmt = $pdo->prepare("UPDATE blogs SET status = ? WHERE id = ? AND author_id = ?");
        if ($stmt->execute([$new_status, $blog_id, $user['id']])) {
            $success = 'Blog status updated successfully!';
        } else {
            $error = 'Failed to update blog status.';
        }
    } else {
        $error = 'Invalid parameters.';
    }
}

// Get user's blog posts
$stmt = $pdo->prepare("
    SELECT * FROM blogs 
    WHERE author_id = ? 
    ORDER BY created_at DESC
");
$stmt->execute([$user['id']]);
$my_blogs = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = 'My Blogs';
include 'includes/header.php';
?>

<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4><i class="fas fa-list me-2"></i>My Blog Posts</h4>
                    <a href="write_blog.php" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>New Post
                    </a>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                        </div>
                    <?php endif; ?>

                    <?php if (empty($my_blogs)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-blog fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No blog posts yet</h5>
                            <p class="text-muted">Start sharing your cybersecurity insights with the community!</p>
                            <a href="write_blog.php" class="btn btn-primary">
                                <i class="fas fa-pen me-2"></i>Write Your First Post
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="row g-4">
                            <?php foreach ($my_blogs as $blog): ?>
                            <div class="col-lg-6">
                                <div class="card bg-dark h-100">
                                    <div class="card-body d-flex flex-column">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <span class="badge bg-secondary"><?php echo htmlspecialchars($blog['category']); ?></span>
                                            <div>
                                                <span class="badge <?php echo $blog['status'] === 'published' ? 'bg-success' : ($blog['status'] === 'pending' ? 'bg-warning text-dark' : 'bg-secondary'); ?>">
                                                    <?php echo htmlspecialchars($blog['status']); ?>
                                                </span>
                                                <?php if ($blog['is_premium']): ?>
                                                    <span class="badge bg-warning text-dark ms-1">
                                                        <i class="fas fa-crown me-1"></i>Premium
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        
                                        <h5 class="card-title text-primary"><?php echo htmlspecialchars($blog['title']); ?></h5>
                                        <p class="card-text flex-grow-1"><?php echo htmlspecialchars($blog['excerpt']); ?></p>
                                        
                                        <div class="mt-auto">
                                            <small class="text-muted d-block mb-3">
                                                <i class="fas fa-calendar me-1"></i>Created: <?php echo date('M j, Y', strtotime($blog['created_at'])); ?>
                                                <?php if ($blog['updated_at'] !== $blog['created_at']): ?>
                                                    <br><i class="fas fa-edit me-1"></i>Updated: <?php echo date('M j, Y', strtotime($blog['updated_at'])); ?>
                                                <?php endif; ?>
                                            </small>
                                            
                                            <div class="btn-group w-100" role="group">
                                                <?php if ($blog['status'] === 'published'): ?>
                                                    <a href="blog.php?id=<?php echo $blog['id']; ?>" class="btn btn-sm btn-outline-primary" target="_blank">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                <?php endif; ?>
                                                
                                                <a href="edit_blog.php?id=<?php echo $blog['id']; ?>" class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                
                                                <a href="?toggle=<?php echo $blog['id']; ?>&status=<?php echo $blog['status']; ?>" 
                                                   class="btn btn-sm btn-outline-warning">
                                                    <i class="fas fa-toggle-<?php echo $blog['status'] === 'published' ? 'on' : 'off'; ?>"></i>
                                                </a>
                                                
                                                <a href="?delete=<?php echo $blog['id']; ?>" 
                                                   class="btn btn-sm btn-outline-danger"
                                                   onclick="return confirm('Are you sure you want to delete this blog post? This action cannot be undone.')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="mt-4">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>Blog Statistics</h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>Total Posts:</strong> <?php echo count($my_blogs); ?>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Published:</strong> <?php echo count(array_filter($my_blogs, function($b) { return $b['status'] === 'published'; })); ?>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Pending:</strong> <?php echo count(array_filter($my_blogs, function($b) { return $b['status'] === 'pending'; })); ?>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Drafts:</strong> <?php echo count(array_filter($my_blogs, function($b) { return $b['status'] === 'draft'; })); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <div class="text-center mt-4">
                        <a href="dashboard.php" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
