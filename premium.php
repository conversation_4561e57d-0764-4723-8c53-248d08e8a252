<?php
require_once 'config.php';
requireLogin();

$user = getCurrentUser();
$success = '';
$error = '';
$blog_id = filter_var($_GET['blog_id'] ?? 0, FILTER_VALIDATE_INT);

// VULNERABILITY: Payment bypass - if payment_status=completed is sent, grant access without validation
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['payment_status']) && $_POST['payment_status'] === 'completed') {
        // Vulnerable code: trusts client-side payment status
        if (grantPremiumAccess($user['id'], 30)) {
            $success = 'Premium access granted! You now have access to all premium content.';
        } else {
            $error = 'Failed to grant premium access.';
        }
    } else {
        // Simulate payment processing
        $amount = filter_var($_POST['amount'] ?? 0, FILTER_VALIDATE_FLOAT);
        $payment_method = filter_var($_POST['payment_method'], FILTER_SANITIZE_STRING);
        
        if ($amount >= 9.99) {
            // Insert payment record
            $stmt = $pdo->prepare("INSERT INTO user_payments (user_id, amount, payment_method, payment_status, transaction_id) VALUES (?, ?, ?, 'pending', ?)");
            $transaction_id = 'TXN_' . uniqid();
            $stmt->execute([$user['id'], $amount, $payment_method, $transaction_id]);
            
            $error = 'Payment processing... Please wait for confirmation.';
        } else {
            $error = 'Minimum payment amount is $9.99';
        }
    }
}

// Check if user already has premium access
$has_premium = hasPremiumAccess($user['id']);

$page_title = 'Premium Access';
include 'includes/header.php';
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <?php if ($has_premium): ?>
                <!-- User already has premium access -->
                <div class="card">
                    <div class="card-header text-center">
                        <h4><i class="fas fa-crown text-warning me-2"></i>Premium Member</h4>
                    </div>
                    <div class="card-body text-center">
                        <div class="alert alert-success">
                            <h5><i class="fas fa-check-circle me-2"></i>You have Premium Access!</h5>
                            <p class="mb-0">Enjoy unlimited access to all premium cybersecurity content.</p>
                        </div>
                        
                        <?php if ($blog_id): ?>
                            <a href="blog.php?id=<?php echo $blog_id; ?>" class="btn btn-primary btn-lg">
                                <i class="fas fa-book-open me-2"></i>Read Premium Article
                            </a>
                        <?php else: ?>
                            <a href="index.php" class="btn btn-primary btn-lg">
                                <i class="fas fa-home me-2"></i>Browse All Content
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            <?php else: ?>
                <!-- Premium upgrade page -->
                <div class="card">
                    <div class="card-header text-center">
                        <h4><i class="fas fa-crown text-warning me-2"></i>Upgrade to Premium</h4>
                    </div>
                    <div class="card-body">
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                            </div>
                            <div class="text-center">
                                <?php if ($blog_id): ?>
                                    <a href="blog.php?id=<?php echo $blog_id; ?>" class="btn btn-primary btn-lg">
                                        <i class="fas fa-book-open me-2"></i>Read Premium Article
                                    </a>
                                <?php else: ?>
                                    <a href="index.php" class="btn btn-primary btn-lg">
                                        <i class="fas fa-home me-2"></i>Browse All Content
                                    </a>
                                <?php endif; ?>
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card bg-dark">
                                        <div class="card-body">
                                            <h5 class="text-warning"><i class="fas fa-star me-2"></i>Premium Benefits</h5>
                                            <ul class="list-unstyled">
                                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Access to exclusive premium articles</li>
                                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Advanced exploitation techniques</li>
                                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Real-world case studies</li>
                                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Expert insights and analysis</li>
                                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>30-day access period</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-credit-card me-2"></i>Payment Details</h6>
                                        </div>
                                        <div class="card-body">
                                            <form method="POST" id="paymentForm">
                                                <div class="mb-3">
                                                    <label class="form-label">Premium Access</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">$</span>
                                                        <input type="number" class="form-control" name="amount" value="9.99" step="0.01" min="9.99" required>
                                                    </div>
                                                </div>
                                                
                                                <div class="mb-3">
                                                    <label class="form-label">Payment Method</label>
                                                    <select class="form-control" name="payment_method" required>
                                                        <option value="">Select Payment Method</option>
                                                        <option value="credit_card">Credit Card</option>
                                                        <option value="paypal">PayPal</option>
                                                        <option value="crypto">Cryptocurrency</option>
                                                    </select>
                                                </div>
                                                
                                                <div class="d-grid">
                                                    <button type="submit" class="btn btn-warning btn-lg">
                                                        <i class="fas fa-crown me-2"></i>Upgrade Now - $9.99
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>Secure Payment</h6>
                                    <p class="mb-0">Your payment is processed securely. Premium access will be activated immediately upon successful payment.</p>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// VULNERABILITY HINT: Payment bypass through client-side manipulation
document.getElementById('paymentForm')?.addEventListener('submit', function(e) {
    // This is where a security researcher might discover the payment bypass
    console.log('Payment form submitted');
});
</script>

<?php include 'includes/footer.php'; ?>
