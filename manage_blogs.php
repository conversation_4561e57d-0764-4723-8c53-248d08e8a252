<?php
require_once 'config.php';
requireLogin();

$user = getCurrentUser();

// Only admins can manage blogs
if ($user['role'] !== 'admin') {
    header('Location: dashboard.php');
    exit();
}

$success = '';
$error = '';

// Handle blog creation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'create') {
    $title = trim($_POST['title']);
    $content = trim($_POST['content']);
    $excerpt = trim($_POST['excerpt']);
    $category = trim($_POST['category']);
    $status = $_POST['status'];
    
    if (empty($title) || empty($content) || empty($excerpt) || empty($category)) {
        $error = 'All fields are required.';
    } else {
        $stmt = $pdo->prepare("INSERT INTO blogs (title, content, excerpt, category, author_id, status) VALUES (?, ?, ?, ?, ?, ?)");
        if ($stmt->execute([$title, $content, $excerpt, $category, $user['id'], $status])) {
            $success = 'Blog post created successfully!';
        } else {
            $error = 'Failed to create blog post.';
        }
    }
}

// Handle blog deletion
if (isset($_GET['delete'])) {
    $blog_id = (int)$_GET['delete'];
    $stmt = $pdo->prepare("DELETE FROM blogs WHERE id = ?");
    if ($stmt->execute([$blog_id])) {
        $success = 'Blog post deleted successfully!';
    } else {
        $error = 'Failed to delete blog post.';
    }
}

// Get all blog posts
$stmt = $pdo->prepare("
    SELECT b.*, u.name as author_name 
    FROM blogs b 
    JOIN users u ON b.author_id = u.id 
    ORDER BY b.created_at DESC
");
$stmt->execute();
$blogs = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = 'Manage Blogs';
include 'includes/header.php';
?>

<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header">
                    <h4><i class="fas fa-blog me-2"></i>Blog Management</h4>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                        </div>
                    <?php endif; ?>

                    <!-- Create New Blog Form -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-plus me-2"></i>Create New Blog Post</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <input type="hidden" name="action" value="create">
                                
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label for="title" class="form-label">Title</label>
                                            <input type="text" class="form-control" id="title" name="title" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="category" class="form-label">Category</label>
                                            <input type="text" class="form-control" id="category" name="category" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="excerpt" class="form-label">Excerpt</label>
                                    <textarea class="form-control" id="excerpt" name="excerpt" rows="2" required></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="content" class="form-label">Content</label>
                                    <textarea class="form-control" id="content" name="content" rows="8" required></textarea>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="status" class="form-label">Status</label>
                                            <select class="form-control" id="status" name="status">
                                                <option value="published">Published</option>
                                                <option value="draft">Draft</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6 d-flex align-items-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Create Blog Post
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Existing Blogs -->
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-list me-2"></i>Existing Blog Posts</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($blogs)): ?>
                                <p class="text-muted">No blog posts found.</p>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-dark table-striped">
                                        <thead>
                                            <tr>
                                                <th>Title</th>
                                                <th>Category</th>
                                                <th>Author</th>
                                                <th>Status</th>
                                                <th>Created</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($blogs as $blog): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($blog['title']); ?></td>
                                                <td><span class="badge bg-secondary"><?php echo htmlspecialchars($blog['category']); ?></span></td>
                                                <td><?php echo htmlspecialchars($blog['author_name']); ?></td>
                                                <td>
                                                    <span class="badge <?php echo $blog['status'] === 'published' ? 'bg-success' : 'bg-warning'; ?>">
                                                        <?php echo htmlspecialchars($blog['status']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo date('M j, Y', strtotime($blog['created_at'])); ?></td>
                                                <td>
                                                    <a href="blog.php?id=<?php echo $blog['id']; ?>" class="btn btn-sm btn-outline-primary" target="_blank">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="?delete=<?php echo $blog['id']; ?>" class="btn btn-sm btn-outline-danger" 
                                                       onclick="return confirm('Are you sure you want to delete this blog post?')">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
