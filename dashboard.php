<?php
require_once 'config.php';
requireLogin();

$user = getCurrentUser();
$page_title = 'Dashboard';
include 'includes/header.php';
?>

<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header">
                    <h4><i class="fas fa-tachometer-alt me-2"></i>Dashboard</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h5>Welcome, <?php echo htmlspecialchars($user['name']); ?>!</h5>
                            <p class="text-muted">
                                <i class="fas fa-user me-1"></i>Username: <?php echo htmlspecialchars($user['username']); ?><br>
                                <i class="fas fa-envelope me-1"></i>Email: <?php echo htmlspecialchars($user['email']); ?><br>
                                <i class="fas fa-shield-alt me-1"></i>Role: 
                                <span class="badge <?php echo $user['role'] === 'admin' ? 'bg-danger' : 'bg-secondary'; ?>">
                                    <?php echo htmlspecialchars($user['role']); ?>
                                </span>
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="profile.php" class="btn btn-primary">
                                <i class="fas fa-edit me-2"></i>Edit Profile
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <?php if ($user['role'] === 'admin'): ?>
            <!-- Admin Content -->
            <div class="col-12">
                <div class="card border-danger">
                    <div class="card-header bg-danger text-white">
                        <h5><i class="fas fa-crown me-2"></i>Admin Panel</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success text-center">
                            <h4><i class="fas fa-flag me-2"></i>Congratulations!</h4>
                            <p class="mb-0">You have successfully escalated your privileges!</p>
                            <hr>
                            <h3 class="cyber-glow">🚩 flag{role_escalation_via_profile_update} 🚩</h3>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="card bg-dark">
                                    <div class="card-body">
                                        <h6><i class="fas fa-users me-2"></i>User Management</h6>
                                        <p class="small text-muted">As an admin, you would have access to user management features.</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-dark">
                                    <div class="card-body">
                                        <h6><i class="fas fa-cogs me-2"></i>System Settings</h6>
                                        <p class="small text-muted">Administrative settings and configurations would be available here.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- Regular User Content -->
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-user me-2"></i>User Dashboard</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Welcome to the Lab!</h6>
                            <p class="mb-0">
                                You are currently logged in as a regular user. Your goal is to find a way to 
                                escalate your privileges to admin level and capture the flag.
                            </p>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="card bg-dark">
                                    <div class="card-body">
                                        <h6><i class="fas fa-book me-2"></i>Learning Resources</h6>
                                        <p class="small text-muted">Access to basic learning materials and tutorials.</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-dark">
                                    <div class="card-body">
                                        <h6><i class="fas fa-user-edit me-2"></i>Profile Settings</h6>
                                        <p class="small text-muted">Update your profile information and preferences.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-trophy me-2"></i>Challenge</h6>
                                <p class="mb-0">
                                    Your mission is to find a way to gain administrative privileges.
                                    Explore the application and see what you can discover!
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
