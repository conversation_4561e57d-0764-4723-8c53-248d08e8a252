<?php
require_once 'config.php';
requireLogin();

$user = getCurrentUser();
$success = '';
$error = '';

// Handle blog creation
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize inputs
    $title = filter_var(trim($_POST['title']), FILTER_SANITIZE_STRING);
    $excerpt = filter_var(trim($_POST['excerpt']), FILTER_SANITIZE_STRING);
    $category = filter_var(trim($_POST['category']), FILTER_SANITIZE_STRING);
    $status = filter_var($_POST['status'], FILTER_SANITIZE_STRING);
    
    // For content, we allow HTML but sanitize it properly
    $content = trim($_POST['content']);
    // Remove potentially dangerous HTML tags and attributes
    $content = strip_tags($content, '<p><br><h3><h4><h5><strong><em><ul><ol><li><blockquote><code><pre>');
    
    // Validation
    if (empty($title) || empty($content) || empty($excerpt) || empty($category)) {
        $error = 'All fields are required.';
    } elseif (strlen($title) < 5 || strlen($title) > 255) {
        $error = 'Title must be between 5 and 255 characters long.';
    } elseif (strlen($excerpt) < 10 || strlen($excerpt) > 500) {
        $error = 'Excerpt must be between 10 and 500 characters long.';
    } elseif (strlen($category) < 2 || strlen($category) > 50) {
        $error = 'Category must be between 2 and 50 characters long.';
    } elseif (!in_array($status, ['draft', 'pending'])) {
        $error = 'Invalid status selected.';
    } else {
        $stmt = $pdo->prepare("INSERT INTO blogs (title, content, excerpt, category, author_id, status) VALUES (?, ?, ?, ?, ?, ?)");
        if ($stmt->execute([$title, $content, $excerpt, $category, $user['id'], $status])) {
            $success = 'Blog post created successfully!';
            // Clear form data
            $_POST = [];
        } else {
            $error = 'Failed to create blog post. Please try again.';
        }
    }
}

$page_title = 'Write Blog';
include 'includes/header.php';
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-pen me-2"></i>Write New Blog Post</h4>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                        </div>
                        <div class="text-center">
                            <a href="my_blogs.php" class="btn btn-primary me-2">
                                <i class="fas fa-list me-1"></i>View My Blogs
                            </a>
                            <a href="dashboard.php" class="btn btn-outline-light">
                                <i class="fas fa-tachometer-alt me-1"></i>Back to Dashboard
                            </a>
                        </div>
                    <?php else: ?>
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="title" class="form-label">
                                            <i class="fas fa-heading me-1"></i>Title
                                        </label>
                                        <input type="text" class="form-control" id="title" name="title" 
                                               value="<?php echo htmlspecialchars($_POST['title'] ?? ''); ?>" 
                                               placeholder="Enter your blog post title" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="category" class="form-label">
                                            <i class="fas fa-tag me-1"></i>Category
                                        </label>
                                        <input type="text" class="form-control" id="category" name="category" 
                                               value="<?php echo htmlspecialchars($_POST['category'] ?? ''); ?>" 
                                               placeholder="e.g., Web Security" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="excerpt" class="form-label">
                                    <i class="fas fa-quote-left me-1"></i>Excerpt
                                </label>
                                <textarea class="form-control" id="excerpt" name="excerpt" rows="3" 
                                          placeholder="Write a brief summary of your blog post (this will appear on the homepage)"
                                          required><?php echo htmlspecialchars($_POST['excerpt'] ?? ''); ?></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="content" class="form-label">
                                    <i class="fas fa-file-alt me-1"></i>Content
                                </label>
                                <textarea class="form-control" id="content" name="content" rows="12" 
                                          placeholder="Write your blog post content here. You can use HTML tags for formatting."
                                          required><?php echo htmlspecialchars($_POST['content'] ?? ''); ?></textarea>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    You can use HTML tags like &lt;h3&gt;, &lt;p&gt;, &lt;ul&gt;, &lt;li&gt;, &lt;strong&gt; for formatting.
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="status" class="form-label">
                                            <i class="fas fa-eye me-1"></i>Status
                                        </label>
                                        <select class="form-control" id="status" name="status">
                                            <option value="pending" <?php echo (($_POST['status'] ?? 'pending') === 'pending') ? 'selected' : ''; ?>>
                                                Pending Approval (Awaiting admin review)
                                            </option>
                                            <option value="draft" <?php echo (($_POST['status'] ?? '') === 'draft') ? 'selected' : ''; ?>>
                                                Draft (Only visible to you)
                                            </option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6 d-flex align-items-end">
                                    <div class="mb-3 w-100">
                                        <button type="submit" class="btn btn-primary me-2">
                                            <i class="fas fa-save me-1"></i>Create Post
                                        </button>
                                        <a href="dashboard.php" class="btn btn-outline-light">
                                            <i class="fas fa-times me-1"></i>Cancel
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Writing Tips -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6><i class="fas fa-lightbulb me-2"></i>Writing Tips</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">Content Guidelines</h6>
                            <ul class="small">
                                <li>Focus on cybersecurity topics</li>
                                <li>Share practical insights and experiences</li>
                                <li>Use clear, professional language</li>
                                <li>Include examples when possible</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary">Approval Process</h6>
                            <ul class="small">
                                <li>Posts are reviewed by administrators</li>
                                <li>Approval typically takes 24-48 hours</li>
                                <li>You'll be notified when your post is approved</li>
                                <li>Save as draft to edit before submission</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
