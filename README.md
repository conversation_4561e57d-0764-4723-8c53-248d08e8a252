# Cyber Bangla Lab - CTF Challenge

A vulnerable PHP blog application designed as a Capture The Flag (CTF) challenge for cybersecurity education.

## 🎯 Challenge Objective

Find a way to escalate your privileges from a regular user to administrator and capture the flag.

## 🚨 Challenge Description

This is a blog application with user registration and authentication. Your goal is to find vulnerabilities that allow privilege escalation and gain administrative access to retrieve the flag.

## 🛠️ Setup Instructions

### Prerequisites
- XAMPP (or similar LAMP/WAMP stack)
- PHP 7.4 or higher
- MySQL 5.7 or higher

### Installation Steps

1. **Clone/Copy the files** to your web server directory:
   ```
   s:\xampp\htdocs\labs\broken-access-control\
   ```

2. **Start XAMPP services**:
   - Apache
   - MySQL

3. **Create the database**:
   - Open phpMyAdmin (http://localhost/phpmyadmin)
   - Import the `database.sql` file or run the SQL commands manually

4. **Configure database connection** (if needed):
   - Edit `config.php` to match your database settings
   - Default settings work with standard XAMPP installation

5. **Access the application**:
   - Open: http://localhost/labs/broken-access-control/

## 🎮 Getting Started

1. Register a new account or use existing credentials
2. Explore the application functionality
3. Look for ways to escalate your privileges
4. Capture the flag when you gain admin access

## 🏆 Flag Format

The flag follows the format: `flag{...}`

## 🏆 Success Criteria

Successfully escalate privileges to admin level and retrieve the flag from the admin dashboard.

## 💡 Learning Outcomes

After completing this challenge, you will understand:
- How broken access control vulnerabilities work
- The importance of proper input validation
- Authorization vs Authentication concepts
- Secure coding practices for user management

## ⚠️ Disclaimer

This application is intentionally vulnerable and designed for educational purposes only. 
**DO NOT** deploy this in a production environment or on publicly accessible servers.

## 👨‍💻 Developer

Developed by **TareqAhamed (0xt4req)** for cybersecurity education.

## 📚 Additional Resources

- [OWASP Top 10 - Broken Access Control](https://owasp.org/Top10/A01_2021-Broken_Access_Control/)
- [OWASP Testing Guide - Authorization Testing](https://owasp.org/www-project-web-security-testing-guide/latest/4-Web_Application_Security_Testing/05-Authorization_Testing/)
- [CWE-639: Authorization Bypass Through User-Controlled Key](https://cwe.mitre.org/data/definitions/639.html)
