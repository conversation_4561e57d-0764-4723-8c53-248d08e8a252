# Cyber Bangla Lab - Broken Access Control Vulnerability

A vulnerable PHP web application designed for cybersecurity education, demonstrating broken access control vulnerabilities.

## 🎯 Learning Objective

This lab teaches about **Broken Access Control** vulnerabilities, specifically privilege escalation through insecure profile update functionality.

## 🚨 Vulnerability Description

The application contains a critical vulnerability in the profile update functionality where:
- Users can escalate their privileges from 'user' to 'admin'
- The backend trusts all POST parameters without proper authorization
- Adding `role=admin` to the profile update request grants admin privileges

## 🛠️ Setup Instructions

### Prerequisites
- XAMPP (or similar LAMP/WAMP stack)
- PHP 7.4 or higher
- MySQL 5.7 or higher

### Installation Steps

1. **Clone/Copy the files** to your web server directory:
   ```
   s:\xampp\htdocs\labs\broken-access-control\
   ```

2. **Start XAMPP services**:
   - Apache
   - MySQL

3. **Create the database**:
   - Open phpMyAdmin (http://localhost/phpmyadmin)
   - Import the `database.sql` file or run the SQL commands manually

4. **Configure database connection** (if needed):
   - Edit `config.php` to match your database settings
   - Default settings work with standard XAMPP installation

5. **Access the application**:
   - Open: http://localhost/labs/broken-access-control/

## 🔐 Demo Accounts

- **Admin Account**: 
  - Username: `admin`
  - Password: `admin123`

- **Regular User**: 
  - Username: `testuser`
  - Password: `user123`

## 🎯 Exploitation Steps

### Method 1: Browser Developer Tools
1. Login as a regular user
2. Go to Profile page
3. Open browser Developer Tools (F12)
4. Navigate to Network tab
5. Submit the profile update form
6. Right-click the POST request → "Edit and Resend"
7. Add `role=admin` to the form data
8. Send the modified request
9. Refresh the page to see admin privileges

### Method 2: Burp Suite/Proxy
1. Configure browser to use Burp Suite proxy
2. Login and navigate to profile update
3. Submit the form and intercept the request
4. Add `role=admin` parameter to the POST data
5. Forward the modified request

### Method 3: cURL
```bash
# First login and get session cookie, then:
curl -X POST http://localhost/labs/broken-access-control/profile.php \
  -H "Cookie: PHPSESSID=your_session_id" \
  -d "name=Your Name&email=<EMAIL>&role=admin"
```

## 🏆 Success Criteria

When exploitation is successful:
- User role changes from 'user' to 'admin'
- Dashboard displays admin content
- Flag is revealed: `flag{role_escalation_via_profile_update}`

## 🛡️ Mitigation Strategies

To fix this vulnerability:

1. **Input Validation**: Only allow expected fields
2. **Authorization Checks**: Verify user permissions before role changes
3. **Whitelist Approach**: Define allowed updateable fields
4. **Separate Admin Functions**: Use dedicated admin interfaces

### Secure Code Example:
```php
// Only allow specific fields to be updated
$allowedFields = ['name', 'email'];
$updateFields = [];
$updateValues = [];

foreach ($allowedFields as $field) {
    if (isset($_POST[$field])) {
        $updateFields[] = "$field = ?";
        $updateValues[] = $_POST[$field];
    }
}

// Role changes require admin privileges
if (isset($_POST['role']) && isAdmin()) {
    $updateFields[] = "role = ?";
    $updateValues[] = $_POST['role'];
}
```

## ⚠️ Disclaimer

This application is intentionally vulnerable and designed for educational purposes only. 
**DO NOT** deploy this in a production environment or on publicly accessible servers.

## 👨‍💻 Developer

Developed by **TareqAhamed (0xt4req)** for cybersecurity education.

## 📚 Additional Resources

- [OWASP Top 10 - Broken Access Control](https://owasp.org/Top10/A01_2021-Broken_Access_Control/)
- [OWASP Testing Guide - Authorization Testing](https://owasp.org/www-project-web-security-testing-guide/latest/4-Web_Application_Security_Testing/05-Authorization_Testing/)
- [CWE-639: Authorization Bypass Through User-Controlled Key](https://cwe.mitre.org/data/definitions/639.html)
