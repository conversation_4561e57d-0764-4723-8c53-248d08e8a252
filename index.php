<?php
require_once 'config.php';
$page_title = 'Home';
include 'includes/header.php';
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="text-center mb-5">
                <h1 class="display-4 cyber-glow mb-4">
                    <i class="fas fa-shield-alt me-3"></i>
                    Welcome to Cyber Bangla Lab
                </h1>
                <p class="lead" style="color: var(--text-muted);">
                    A cybersecurity training platform for learning about web vulnerabilities
                </p>
            </div>

            <div class="row g-4">
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-header">
                            <i class="fas fa-bug me-2"></i>Vulnerability Lab
                        </div>
                        <div class="card-body">
                            <h5 class="card-title text-primary">Broken Access Control</h5>
                            <p class="card-text">
                                This lab demonstrates a common web vulnerability where users can escalate their privileges 
                                through improper access controls in profile update functionality.
                            </p>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Warning:</strong> This is a vulnerable application for educational purposes only.
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-header">
                            <i class="fas fa-graduation-cap me-2"></i>Learning Objectives
                        </div>
                        <div class="card-body">
                            <h5 class="card-title text-primary">What You'll Learn</h5>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Identify broken access control vulnerabilities
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Understand privilege escalation attacks
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Learn secure coding practices
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Practice ethical hacking techniques
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-5">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-rocket me-2"></i>Get Started
                        </div>
                        <div class="card-body text-center">
                            <?php if (isLoggedIn()): ?>
                                <h5 class="card-title">Welcome back!</h5>
                                <p class="card-text">You're already logged in. Access your dashboard to continue.</p>
                                <a href="dashboard.php" class="btn btn-primary btn-lg">
                                    <i class="fas fa-tachometer-alt me-2"></i>Go to Dashboard
                                </a>
                            <?php else: ?>
                                <h5 class="card-title">Ready to Start Learning?</h5>
                                <p class="card-text">Create an account or login to access the vulnerability lab.</p>
                                <div class="d-grid gap-2 d-md-block">
                                    <a href="register.php" class="btn btn-primary btn-lg me-md-2">
                                        <i class="fas fa-user-plus me-2"></i>Register
                                    </a>
                                    <a href="login.php" class="btn btn-outline-light btn-lg">
                                        <i class="fas fa-sign-in-alt me-2"></i>Login
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
