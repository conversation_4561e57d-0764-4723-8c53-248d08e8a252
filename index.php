<?php
require_once 'config.php';
$page_title = 'Home';

// Get published blog posts from database
$stmt = $pdo->prepare("
    SELECT b.*, u.name as author_name
    FROM blogs b
    JOIN users u ON b.author_id = u.id
    WHERE b.status = 'published'
    ORDER BY b.created_at DESC
    LIMIT 6
");
$stmt->execute();
$blog_posts = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Check premium access for current user
$user_has_premium = false;
if (isLoggedIn()) {
    $current_user = getCurrentUser();
    $user_has_premium = hasPremiumAccess($current_user['id']);
}

// Fallback sample posts if database is empty
if (empty($blog_posts)) {
    $blog_posts = [
        [
            'id' => 1,
            'title' => 'Advanced Penetration Testing Techniques',
            'excerpt' => 'Exploring modern methodologies for comprehensive security assessments and vulnerability discovery in enterprise environments.',
            'author_name' => 'TareqAhamed',
            'created_at' => '2024-01-15',
            'category' => 'Penetration Testing'
        ],
        [
            'id' => 2,
            'title' => 'Web Application Security Best Practices',
            'excerpt' => 'Essential security measures every developer should implement to protect web applications from common vulnerabilities.',
            'author_name' => 'CyberExpert',
            'created_at' => '2024-01-12',
            'category' => 'Web Security'
        ],
        [
            'id' => 3,
            'title' => 'OWASP Top 10 2023: What\'s New?',
            'excerpt' => 'A comprehensive analysis of the latest OWASP Top 10 vulnerabilities and their impact on modern web applications.',
            'author_name' => 'SecurityGuru',
            'created_at' => '2024-01-10',
            'category' => 'OWASP'
        ]
    ];
}

include 'includes/header.php';
?>

<div class="container">
    <!-- Hero Section -->
    <div class="row justify-content-center mb-5">
        <div class="col-lg-10 text-center">
            <h1 class="display-4 cyber-glow mb-4">
                <i class="fas fa-shield-alt me-3"></i>
                Cyber Bangla Lab
            </h1>
            <p class="lead" style="color: var(--text-muted);">
                Your premier destination for cybersecurity insights, tutorials, and cutting-edge research
            </p>
        </div>
    </div>

    <!-- Blog Posts Grid -->
    <div class="row g-4 mb-5">
        <?php foreach ($blog_posts as $post): ?>
        <div class="col-lg-4 col-md-6">
            <div class="card h-100">
                <?php
                // Generate placeholder image based on category
                $category_short = substr(str_replace(' ', '', $post['category']), 0, 6);
                $image_url = "https://via.placeholder.com/400x200/0c1a2b/00c9a7?text=" . urlencode($category_short);
                ?>
                <img src="<?php echo $image_url; ?>" class="card-img-top" alt="<?php echo htmlspecialchars($post['title']); ?>" style="height: 200px; object-fit: cover;">
                <div class="card-body d-flex flex-column">
                    <div class="mb-2">
                        <span class="badge bg-primary"><?php echo htmlspecialchars($post['category']); ?></span>
                        <?php if (($post['is_premium'] ?? false)): ?>
                            <span class="badge bg-warning text-dark ms-1">
                                <i class="fas fa-crown me-1"></i>Premium
                            </span>
                        <?php endif; ?>
                    </div>
                    <h5 class="card-title text-primary"><?php echo htmlspecialchars($post['title']); ?></h5>
                    <p class="card-text flex-grow-1"><?php echo htmlspecialchars($post['excerpt']); ?></p>
                    <div class="mt-auto">
                        <small class="text-muted">
                            <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($post['author_name']); ?>
                            <i class="fas fa-calendar ms-3 me-1"></i><?php echo date('M j, Y', strtotime($post['created_at'])); ?>
                        </small>
                        <div class="mt-2">
                            <?php if (($post['is_premium'] ?? false) && !$user_has_premium): ?>
                                <a href="premium.php" class="btn btn-warning btn-sm">
                                    <i class="fas fa-lock me-1"></i>Upgrade to Read
                                </a>
                            <?php else: ?>
                                <a href="blog.php?id=<?php echo $post['id']; ?>" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-book-open me-1"></i>Read More
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>

    <!-- Call to Action -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header text-center">
                    <h4><i class="fas fa-rocket me-2"></i>Join Our Community</h4>
                </div>
                <div class="card-body text-center">
                    <?php if (isLoggedIn()): ?>
                        <h5 class="card-title">Welcome back!</h5>
                        <p class="card-text">Access your dashboard to manage your profile and explore more content.</p>
                        <a href="dashboard.php" class="btn btn-primary btn-lg">
                            <i class="fas fa-tachometer-alt me-2"></i>Go to Dashboard
                        </a>
                    <?php else: ?>
                        <h5 class="card-title">Ready to Get Started?</h5>
                        <p class="card-text">Join our community to access exclusive content and personalized features.</p>
                        <div class="d-grid gap-2 d-md-block">
                            <a href="register.php" class="btn btn-primary btn-lg me-md-2">
                                <i class="fas fa-user-plus me-2"></i>Register
                            </a>
                            <a href="login.php" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>Login
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
