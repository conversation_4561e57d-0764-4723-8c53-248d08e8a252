<?php
require_once 'config.php';
$page_title = 'Home';

// Sample blog posts
$blog_posts = [
    [
        'id' => 1,
        'title' => 'Advanced Penetration Testing Techniques',
        'excerpt' => 'Exploring modern methodologies for comprehensive security assessments and vulnerability discovery in enterprise environments.',
        'author' => 'TareqAhamed',
        'date' => '2024-01-15',
        'category' => 'Penetration Testing',
        'image' => 'https://via.placeholder.com/400x200/0c1a2b/00c9a7?text=PenTest'
    ],
    [
        'id' => 2,
        'title' => 'Web Application Security Best Practices',
        'excerpt' => 'Essential security measures every developer should implement to protect web applications from common vulnerabilities.',
        'author' => 'CyberExpert',
        'date' => '2024-01-12',
        'category' => 'Web Security',
        'image' => 'https://via.placeholder.com/400x200/1e3b5d/00c9a7?text=WebSec'
    ],
    [
        'id' => 3,
        'title' => 'OWASP Top 10 2023: What\'s New?',
        'excerpt' => 'A comprehensive analysis of the latest OWASP Top 10 vulnerabilities and their impact on modern web applications.',
        'author' => 'SecurityGuru',
        'date' => '2024-01-10',
        'category' => 'OWASP',
        'image' => 'https://via.placeholder.com/400x200/0c1a2b/00c9a7?text=OWASP'
    ],
    [
        'id' => 4,
        'title' => 'Incident Response in Cyber Attacks',
        'excerpt' => 'Step-by-step guide to effective incident response procedures and damage mitigation strategies.',
        'author' => 'TareqAhamed',
        'date' => '2024-01-08',
        'category' => 'Incident Response',
        'image' => 'https://via.placeholder.com/400x200/1e3b5d/00c9a7?text=IR'
    ],
    [
        'id' => 5,
        'title' => 'Social Engineering: The Human Factor',
        'excerpt' => 'Understanding psychological manipulation techniques used by attackers and how to build human firewalls.',
        'author' => 'PsychSec',
        'date' => '2024-01-05',
        'category' => 'Social Engineering',
        'image' => 'https://via.placeholder.com/400x200/0c1a2b/00c9a7?text=SocEng'
    ],
    [
        'id' => 6,
        'title' => 'Cloud Security Architecture',
        'excerpt' => 'Designing secure cloud infrastructures with proper access controls, encryption, and monitoring.',
        'author' => 'CloudSecPro',
        'date' => '2024-01-03',
        'category' => 'Cloud Security',
        'image' => 'https://via.placeholder.com/400x200/1e3b5d/00c9a7?text=Cloud'
    ]
];

include 'includes/header.php';
?>

<div class="container">
    <!-- Hero Section -->
    <div class="row justify-content-center mb-5">
        <div class="col-lg-10 text-center">
            <h1 class="display-4 cyber-glow mb-4">
                <i class="fas fa-shield-alt me-3"></i>
                Cyber Bangla Lab
            </h1>
            <p class="lead" style="color: var(--text-muted);">
                Your premier destination for cybersecurity insights, tutorials, and cutting-edge research
            </p>
        </div>
    </div>

    <!-- Blog Posts Grid -->
    <div class="row g-4 mb-5">
        <?php foreach ($blog_posts as $post): ?>
        <div class="col-lg-4 col-md-6">
            <div class="card h-100">
                <img src="<?php echo $post['image']; ?>" class="card-img-top" alt="<?php echo htmlspecialchars($post['title']); ?>" style="height: 200px; object-fit: cover;">
                <div class="card-body d-flex flex-column">
                    <div class="mb-2">
                        <span class="badge bg-primary"><?php echo htmlspecialchars($post['category']); ?></span>
                    </div>
                    <h5 class="card-title text-primary"><?php echo htmlspecialchars($post['title']); ?></h5>
                    <p class="card-text flex-grow-1"><?php echo htmlspecialchars($post['excerpt']); ?></p>
                    <div class="mt-auto">
                        <small class="text-muted">
                            <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($post['author']); ?>
                            <i class="fas fa-calendar ms-3 me-1"></i><?php echo date('M j, Y', strtotime($post['date'])); ?>
                        </small>
                        <div class="mt-2">
                            <a href="#" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-book-open me-1"></i>Read More
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>

    <!-- Call to Action -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header text-center">
                    <h4><i class="fas fa-rocket me-2"></i>Join Our Community</h4>
                </div>
                <div class="card-body text-center">
                    <?php if (isLoggedIn()): ?>
                        <h5 class="card-title">Welcome back!</h5>
                        <p class="card-text">Access your dashboard to manage your profile and explore more content.</p>
                        <a href="dashboard.php" class="btn btn-primary btn-lg">
                            <i class="fas fa-tachometer-alt me-2"></i>Go to Dashboard
                        </a>
                    <?php else: ?>
                        <h5 class="card-title">Ready to Get Started?</h5>
                        <p class="card-text">Join our community to access exclusive content and personalized features.</p>
                        <div class="d-grid gap-2 d-md-block">
                            <a href="register.php" class="btn btn-primary btn-lg me-md-2">
                                <i class="fas fa-user-plus me-2"></i>Register
                            </a>
                            <a href="login.php" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>Login
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
