<?php
require_once 'config.php';
requireLogin();

$user = getCurrentUser();
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name']);
    $email = trim($_POST['email']);
    
    // Basic validation
    if (empty($name) || empty($email)) {
        $error = 'Name and email are required.';
    } else {
        // VULNERABILITY: The application trusts all POST data and doesn't validate/sanitize the role field
        // This allows users to escalate their privileges by including role=admin in the request
        
        // Build the update query dynamically based on POST data
        $updateFields = [];
        $updateValues = [];
        
        // Always update name and email
        $updateFields[] = "name = ?";
        $updateValues[] = $name;
        $updateFields[] = "email = ?";
        $updateValues[] = $email;
        
        // VULNERABLE CODE: Check if role is in POST data and update it without authorization
        if (isset($_POST['role'])) {
            $updateFields[] = "role = ?";
            $updateValues[] = $_POST['role'];
        }
        
        // Add user ID for WHERE clause
        $updateValues[] = $user['id'];
        
        // Execute the update
        $sql = "UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        
        if ($stmt->execute($updateValues)) {
            $success = 'Profile updated successfully!';
            // Refresh user data
            $user = getCurrentUser();
        } else {
            $error = 'Failed to update profile. Please try again.';
        }
    }
}

$page_title = 'Profile';
include 'includes/header.php';
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-user-edit me-2"></i>Update Profile</h4>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <form method="POST" id="profileForm">
                                <div class="mb-3">
                                    <label for="name" class="form-label">
                                        <i class="fas fa-id-card me-1"></i>Full Name
                                    </label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="<?php echo htmlspecialchars($user['name']); ?>" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope me-1"></i>Email
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo htmlspecialchars($user['email']); ?>" required>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Update Profile
                                    </button>
                                </div>
                            </form>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card bg-dark">
                                <div class="card-header">
                                    <h6><i class="fas fa-info-circle me-2"></i>Current Information</h6>
                                </div>
                                <div class="card-body">
                                    <p><strong>Username:</strong> <?php echo htmlspecialchars($user['username']); ?></p>
                                    <p><strong>Email:</strong> <?php echo htmlspecialchars($user['email']); ?></p>
                                    <p><strong>Name:</strong> <?php echo htmlspecialchars($user['name']); ?></p>
                                    <p><strong>Role:</strong> 
                                        <span class="badge <?php echo $user['role'] === 'admin' ? 'bg-danger' : 'bg-secondary'; ?>">
                                            <?php echo htmlspecialchars($user['role']); ?>
                                        </span>
                                    </p>
                                    <p><strong>Member Since:</strong> <?php echo date('M j, Y', strtotime($user['created_at'])); ?></p>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <a href="dashboard.php" class="btn btn-outline-light">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            

        </div>
    </div>
</div>



<?php include 'includes/footer.php'; ?>
