<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?>Cyber Bangla Lab</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-dark: #0c1a2b;
            --secondary-dark: #1e3b5d;
            --accent-teal: #00c9a7;
            --accent-teal-hover: #00b396;
            --text-light: #e8f4f8;
            --text-muted: #a0b4c0;
        }

        body {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--secondary-dark) 100%);
            color: var(--text-light);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: rgba(12, 26, 43, 0.95) !important;
            backdrop-filter: blur(10px);
            border-bottom: 2px solid var(--accent-teal);
        }

        .navbar-brand {
            color: var(--accent-teal) !important;
            font-weight: bold;
            font-size: 1.5rem;
        }

        .navbar-nav .nav-link {
            color: var(--text-light) !important;
            transition: color 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: var(--accent-teal) !important;
        }

        .card {
            background: rgba(30, 59, 93, 0.8);
            border: 1px solid var(--accent-teal);
            backdrop-filter: blur(10px);
        }

        .card-header {
            background: rgba(0, 201, 167, 0.1);
            border-bottom: 1px solid var(--accent-teal);
            color: var(--accent-teal);
            font-weight: bold;
        }

        .btn-primary {
            background: var(--accent-teal);
            border-color: var(--accent-teal);
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: var(--accent-teal-hover);
            border-color: var(--accent-teal-hover);
            transform: translateY(-2px);
        }

        .form-control {
            background: rgba(30, 59, 93, 0.6);
            border: 1px solid var(--accent-teal);
            color: var(--text-light);
        }

        .form-control:focus {
            background: rgba(30, 59, 93, 0.8);
            border-color: var(--accent-teal);
            box-shadow: 0 0 0 0.2rem rgba(0, 201, 167, 0.25);
            color: var(--text-light);
        }

        .form-control::placeholder {
            color: var(--text-muted);
        }

        .alert-success {
            background: rgba(0, 201, 167, 0.2);
            border-color: var(--accent-teal);
            color: var(--text-light);
        }

        .alert-danger {
            background: rgba(220, 53, 69, 0.2);
            border-color: #dc3545;
            color: var(--text-light);
        }

        .text-primary {
            color: var(--accent-teal) !important;
        }

        .cyber-glow {
            text-shadow: 0 0 10px var(--accent-teal);
        }

        .container-fluid {
            min-height: calc(100vh - 120px);
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand cyber-glow" href="index.php">
                <i class="fas fa-shield-alt me-2"></i>Cyber Bangla Lab
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home me-1"></i>Home
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="blogDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-blog me-1"></i>Blogs
                            </a>
                            <ul class="dropdown-menu dropdown-menu-dark">
                                <li><a class="dropdown-item" href="write_blog.php">
                                    <i class="fas fa-pen me-2"></i>Write New Post
                                </a></li>
                                <li><a class="dropdown-item" href="my_blogs.php">
                                    <i class="fas fa-list me-2"></i>My Posts
                                </a></li>
                                <?php if (isAdmin()): ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="manage_blogs.php">
                                    <i class="fas fa-cogs me-2"></i>Manage All Blogs
                                </a></li>
                                <?php endif; ?>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="profile.php">
                                <i class="fas fa-user me-1"></i>Profile
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="logout.php">
                                <i class="fas fa-sign-out-alt me-1"></i>Logout
                            </a>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">
                                <i class="fas fa-sign-in-alt me-1"></i>Login
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">
                                <i class="fas fa-user-plus me-1"></i>Register
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid py-4">
