-- Database setup for vulnerable blog site
CREATE DATABASE IF NOT EXISTS vulnerable_blog;
USE vulnerable_blog;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) NOT NULL,
    password VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    role ENUM('user', 'admin') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert a default admin user (password: admin123)
INSERT INTO users (username, email, password, name, role) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', 'admin');

-- Insert a test user (password: user123)
INSERT INTO users (username, email, password, name, role) VALUES
('testuser', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Test User', 'user');

-- Blogs table
CREATE TABLE IF NOT EXISTS blogs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    category VARCHAR(100),
    author_id INT,
    status ENUM('draft', 'published', 'pending') DEFAULT 'pending',
    is_premium BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (author_id) REFERENCES users(id)
);

-- User payments table for premium features
CREATE TABLE IF NOT EXISTS user_payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    amount DECIMAL(10,2),
    payment_status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    payment_method VARCHAR(50),
    transaction_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Premium access table
CREATE TABLE IF NOT EXISTS premium_access (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    expires_at TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Update existing blogs table to add missing columns if they don't exist
ALTER TABLE blogs
ADD COLUMN IF NOT EXISTS is_premium BOOLEAN DEFAULT FALSE,
MODIFY COLUMN status ENUM('draft', 'published', 'pending') DEFAULT 'pending';

-- Insert sample blog posts (some pending, some premium)
INSERT INTO blogs (title, content, excerpt, category, author_id, status, is_premium) VALUES
('Advanced Penetration Testing Techniques',
'<p>In the ever-evolving landscape of cybersecurity, penetration testing remains one of the most critical components of a comprehensive security strategy. This article explores advanced methodologies that go beyond basic vulnerability scanning.</p>

<h3>Modern Attack Vectors</h3>
<p>Today\'s penetration testers must understand complex attack chains that combine multiple vulnerabilities. Social engineering, physical security bypasses, and advanced persistent threats (APTs) require sophisticated testing approaches.</p>

<h3>Methodology Framework</h3>
<p>A structured approach to penetration testing includes:</p>
<ul>
<li>Reconnaissance and information gathering</li>
<li>Vulnerability assessment and analysis</li>
<li>Exploitation and privilege escalation</li>
<li>Post-exploitation and persistence</li>
<li>Reporting and remediation guidance</li>
</ul>

<p>The key to successful penetration testing lies in thinking like an attacker while maintaining the ethical boundaries of authorized testing.</p>',
'Exploring modern methodologies for comprehensive security assessments and vulnerability discovery in enterprise environments.',
'Penetration Testing', 1, 'published', FALSE),

('Web Application Security Best Practices',
'<p>Web applications face an unprecedented number of security threats. From injection attacks to broken authentication, developers must implement robust security measures from the ground up.</p>

<h3>The OWASP Foundation</h3>
<p>The Open Web Application Security Project (OWASP) provides invaluable resources for developers and security professionals. Their Top 10 list serves as a baseline for understanding the most critical web application security risks.</p>

<h3>Secure Development Lifecycle</h3>
<p>Implementing security throughout the development process includes:</p>
<ul>
<li>Threat modeling during design phase</li>
<li>Secure coding practices</li>
<li>Regular security testing</li>
<li>Code reviews and static analysis</li>
<li>Runtime application self-protection (RASP)</li>
</ul>

<p>Remember: security is not a feature to be added later, but a fundamental requirement that must be built into every layer of your application.</p>',
'Essential security measures every developer should implement to protect web applications from common vulnerabilities.',
'Web Security', 1, 'published', FALSE),

('OWASP Top 10 2023: What\'s New?',
'<p>The OWASP Top 10 for 2023 brings significant updates that reflect the current threat landscape. Understanding these changes is crucial for security professionals and developers alike.</p>

<h3>Key Changes</h3>
<p>Notable updates in the 2023 edition include enhanced focus on:</p>
<ul>
<li>Supply chain vulnerabilities</li>
<li>Server-side request forgery (SSRF)</li>
<li>Insecure design patterns</li>
<li>Software and data integrity failures</li>
</ul>

<h3>Implementation Strategies</h3>
<p>Organizations should prioritize these vulnerabilities based on their specific risk profile and implement appropriate controls. Regular assessment and continuous monitoring are essential components of an effective security program.</p>',
'A comprehensive analysis of the latest OWASP Top 10 vulnerabilities and their impact on modern web applications.',
'OWASP', 1, 'published', FALSE),

('Premium: Advanced Zero-Day Exploitation Techniques',
'<p>This premium content covers advanced zero-day exploitation techniques used by APT groups and nation-state actors.</p>

<h3>Exclusive Content</h3>
<p>Learn about:</p>
<ul>
<li>Memory corruption exploitation</li>
<li>Kernel privilege escalation</li>
<li>Bypass techniques for modern mitigations</li>
<li>Real-world case studies</li>
</ul>

<p>This content is only available to premium subscribers.</p>',
'Advanced zero-day exploitation techniques and real-world case studies from APT campaigns.',
'Advanced Exploitation', 1, 'published', TRUE),

('Premium: Insider Threat Detection Methods',
'<p>Comprehensive guide to detecting and mitigating insider threats in enterprise environments.</p>

<h3>Detection Strategies</h3>
<p>Advanced techniques for identifying malicious insiders and preventing data exfiltration.</p>',
'Learn advanced insider threat detection and prevention strategies used by Fortune 500 companies.',
'Insider Threats', 1, 'published', TRUE);
