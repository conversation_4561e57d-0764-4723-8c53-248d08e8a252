<?php
// Database configuration
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'vulnerable_blog');

// Start session
session_start();

// Database connection
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

// Helper functions
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: login.php');
        exit();
    }
}

function getCurrentUser() {
    global $pdo;
    if (!isLoggedIn()) {
        return null;
    }
    
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function isAdmin() {
    $user = getCurrentUser();
    return $user && $user['role'] === 'admin';
}

// CSRF Protection functions
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// XSS Protection function
function sanitizeOutput($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

// Premium access functions
function hasPremiumAccess($user_id) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM premium_access WHERE user_id = ? AND is_active = 1 AND expires_at > NOW()");
    $stmt->execute([$user_id]);
    return $stmt->fetch() !== false;
}

function grantPremiumAccess($user_id, $days = 30) {
    global $pdo;
    $expires_at = date('Y-m-d H:i:s', strtotime("+{$days} days"));
    $stmt = $pdo->prepare("INSERT INTO premium_access (user_id, expires_at) VALUES (?, ?) ON DUPLICATE KEY UPDATE expires_at = ?, is_active = 1");
    return $stmt->execute([$user_id, $expires_at, $expires_at]);
}
?>
