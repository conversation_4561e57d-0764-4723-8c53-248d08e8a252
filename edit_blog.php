<?php
require_once 'config.php';
requireLogin();

$user = getCurrentUser();
$success = '';
$error = '';
$blog_id = filter_var($_GET['id'] ?? 0, FILTER_VALIDATE_INT);

if (!$blog_id || $blog_id <= 0) {
    header('Location: my_blogs.php');
    exit();
}

// VULNERABILITY: IDOR - No check if the current user owns this blog
// Users can edit any blog by changing the ID in the URL
$stmt = $pdo->prepare("SELECT * FROM blogs WHERE id = ?");
$stmt->execute([$blog_id]);
$blog = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$blog) {
    header('Location: my_blogs.php');
    exit();
}

// Handle blog update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize inputs
    $title = filter_var(trim($_POST['title']), FILTER_SANITIZE_STRING);
    $excerpt = filter_var(trim($_POST['excerpt']), FILTER_SANITIZE_STRING);
    $category = filter_var(trim($_POST['category']), FILTER_SANITIZE_STRING);
    $status = filter_var($_POST['status'], FILTER_SANITIZE_STRING);
    
    // For content, we allow HTML but sanitize it properly
    $content = trim($_POST['content']);
    $content = strip_tags($content, '<p><br><h3><h4><h5><strong><em><ul><ol><li><blockquote><code><pre>');
    
    // Validation
    if (empty($title) || empty($content) || empty($excerpt) || empty($category)) {
        $error = 'All fields are required.';
    } elseif (strlen($title) < 5 || strlen($title) > 255) {
        $error = 'Title must be between 5 and 255 characters long.';
    } elseif (strlen($excerpt) < 10 || strlen($excerpt) > 500) {
        $error = 'Excerpt must be between 10 and 500 characters long.';
    } elseif (strlen($category) < 2 || strlen($category) > 50) {
        $error = 'Category must be between 2 and 50 characters long.';
    } elseif (!in_array($status, ['draft', 'published', 'pending'])) {
        $error = 'Invalid status selected.';
    } else {
        // VULNERABLE: Update without checking ownership
        $stmt = $pdo->prepare("UPDATE blogs SET title = ?, content = ?, excerpt = ?, category = ?, status = ?, updated_at = NOW() WHERE id = ?");
        if ($stmt->execute([$title, $content, $excerpt, $category, $status, $blog_id])) {
            $success = 'Blog post updated successfully!';
            // Refresh blog data
            $stmt = $pdo->prepare("SELECT * FROM blogs WHERE id = ?");
            $stmt->execute([$blog_id]);
            $blog = $stmt->fetch(PDO::FETCH_ASSOC);
        } else {
            $error = 'Failed to update blog post. Please try again.';
        }
    }
}

$page_title = 'Edit Blog';
include 'includes/header.php';
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4><i class="fas fa-edit me-2"></i>Edit Blog Post</h4>
                    <div>
                        <span class="badge <?php echo $blog['status'] === 'published' ? 'bg-success' : ($blog['status'] === 'pending' ? 'bg-warning text-dark' : 'bg-secondary'); ?>">
                            <?php echo htmlspecialchars($blog['status']); ?>
                        </span>
                        <?php if ($blog['is_premium']): ?>
                            <span class="badge bg-warning text-dark ms-1">
                                <i class="fas fa-crown me-1"></i>Premium
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="title" class="form-label">
                                        <i class="fas fa-heading me-1"></i>Title
                                    </label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           value="<?php echo htmlspecialchars($blog['title']); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="category" class="form-label">
                                        <i class="fas fa-tag me-1"></i>Category
                                    </label>
                                    <input type="text" class="form-control" id="category" name="category" 
                                           value="<?php echo htmlspecialchars($blog['category']); ?>" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="excerpt" class="form-label">
                                <i class="fas fa-quote-left me-1"></i>Excerpt
                            </label>
                            <textarea class="form-control" id="excerpt" name="excerpt" rows="3" required><?php echo htmlspecialchars($blog['excerpt']); ?></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="content" class="form-label">
                                <i class="fas fa-file-alt me-1"></i>Content
                            </label>
                            <textarea class="form-control" id="content" name="content" rows="12" required><?php echo htmlspecialchars($blog['content']); ?></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">
                                        <i class="fas fa-eye me-1"></i>Status
                                    </label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="published" <?php echo $blog['status'] === 'published' ? 'selected' : ''; ?>>Published</option>
                                        <option value="draft" <?php echo $blog['status'] === 'draft' ? 'selected' : ''; ?>>Draft</option>
                                        <option value="pending" <?php echo $blog['status'] === 'pending' ? 'selected' : ''; ?>>Pending Approval</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6 d-flex align-items-end">
                                <div class="mb-3 w-100">
                                    <button type="submit" class="btn btn-primary me-2">
                                        <i class="fas fa-save me-1"></i>Update Post
                                    </button>
                                    <a href="my_blogs.php" class="btn btn-outline-light">
                                        <i class="fas fa-times me-1"></i>Cancel
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Blog Info -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6><i class="fas fa-info-circle me-2"></i>Blog Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Blog ID:</strong> <?php echo $blog['id']; ?></p>
                            <p><strong>Author ID:</strong> <?php echo $blog['author_id']; ?></p>
                            <p><strong>Created:</strong> <?php echo date('M j, Y H:i', strtotime($blog['created_at'])); ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Last Updated:</strong> <?php echo date('M j, Y H:i', strtotime($blog['updated_at'])); ?></p>
                            <p><strong>Premium:</strong> <?php echo $blog['is_premium'] ? 'Yes' : 'No'; ?></p>
                            <p><strong>Current Status:</strong> <?php echo htmlspecialchars($blog['status']); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
