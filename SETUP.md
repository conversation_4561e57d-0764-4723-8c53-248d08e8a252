# Setup Instructions

## Quick Setup

1. **Import Database**:
   - Open phpMyAdmin
   - Create a new database called `vulnerable_blog`
   - Import the `database.sql` file

2. **If you get "is_premium" errors**:
   - Visit: `http://localhost/labs/broken-access-control/update_database.php`
   - This will update your database schema automatically

3. **Access the Application**:
   - Visit: `http://localhost/labs/broken-access-control/`

## Demo Accounts

- **Admin**: `admin` / `admin123`
- **User**: `testuser` / `user123`

## Vulnerabilities to Discover

1. **Role Escalation** (Profile Update)
2. **Broken Access Control** (Post Approval)
3. **IDOR** (Edit Other Users' Blogs)
4. **Payment Bypass** (Premium Access)

## Flags

- Main flag is in the admin dashboard after role escalation
- Additional challenges unlock premium content and admin features

## Troubleshooting

If you see database errors:
1. Run `update_database.php` first
2. Check database connection in `config.php`
3. Ensure all tables are created properly
