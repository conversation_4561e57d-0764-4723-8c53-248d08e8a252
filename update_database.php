<?php
require_once 'config.php';

echo "<h2>Database Update Script</h2>";
echo "<p>Updating database schema for new features...</p>";

try {
    // Update blogs table to add missing columns
    $pdo->exec("ALTER TABLE blogs 
                ADD COLUMN IF NOT EXISTS is_premium BOOLEAN DEFAULT FALSE");
    echo "<p>✅ Added is_premium column to blogs table</p>";
    
    // Update status enum to include 'pending'
    $pdo->exec("ALTER TABLE blogs 
                MODIFY COLUMN status ENUM('draft', 'published', 'pending') DEFAULT 'pending'");
    echo "<p>✅ Updated status column to include 'pending'</p>";
    
    // Create user_payments table if not exists
    $pdo->exec("CREATE TABLE IF NOT EXISTS user_payments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        amount DECIMAL(10,2),
        payment_status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
        payment_method VARCHAR(50),
        transaction_id VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
    )");
    echo "<p>✅ Created user_payments table</p>";
    
    // Create premium_access table if not exists
    $pdo->exec("CREATE TABLE IF NOT EXISTS premium_access (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        expires_at TIMESTAMP,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
    )");
    echo "<p>✅ Created premium_access table</p>";
    
    // Update existing blog records to have is_premium = FALSE if NULL
    $pdo->exec("UPDATE blogs SET is_premium = FALSE WHERE is_premium IS NULL");
    echo "<p>✅ Updated existing blog records</p>";
    
    // Insert sample premium content if not exists
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM blogs WHERE is_premium = TRUE");
    $stmt->execute();
    $premium_count = $stmt->fetchColumn();
    
    if ($premium_count == 0) {
        $pdo->exec("INSERT INTO blogs (title, content, excerpt, category, author_id, status, is_premium) VALUES 
            ('Premium: Advanced Zero-Day Exploitation Techniques', 
            '<p>This premium content covers advanced zero-day exploitation techniques used by APT groups and nation-state actors.</p>
            <h3>Exclusive Content</h3>
            <p>Learn about:</p>
            <ul>
            <li>Memory corruption exploitation</li>
            <li>Kernel privilege escalation</li>
            <li>Bypass techniques for modern mitigations</li>
            <li>Real-world case studies</li>
            </ul>
            <p>This content is only available to premium subscribers.</p>', 
            'Advanced zero-day exploitation techniques and real-world case studies from APT campaigns.', 
            'Advanced Exploitation', 1, 'published', TRUE),
            
            ('Premium: Insider Threat Detection Methods', 
            '<p>Comprehensive guide to detecting and mitigating insider threats in enterprise environments.</p>
            <h3>Detection Strategies</h3>
            <p>Advanced techniques for identifying malicious insiders and preventing data exfiltration.</p>', 
            'Learn advanced insider threat detection and prevention strategies used by Fortune 500 companies.', 
            'Insider Threats', 1, 'published', TRUE)");
        echo "<p>✅ Added sample premium content</p>";
    }
    
    echo "<h3>✅ Database update completed successfully!</h3>";
    echo "<p><a href='index.php'>← Back to Home</a></p>";
    
} catch (Exception $e) {
    echo "<p>❌ Error updating database: " . $e->getMessage() . "</p>";
    echo "<p>This might be normal if the columns already exist.</p>";
}
?>
