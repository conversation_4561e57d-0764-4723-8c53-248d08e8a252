<?php
require_once 'config.php';

// VULNERABILITY: No authentication or authorization check
// Any user can access this endpoint and approve posts

$success = '';
$error = '';

if (isset($_GET['post']) && isset($_GET['id'])) {
    $post_id = filter_var($_GET['id'], FILTER_VALIDATE_INT);
    
    if ($post_id && $post_id > 0) {
        // VULNERABLE: No check if user is admin or has permission to approve posts
        $stmt = $pdo->prepare("UPDATE blogs SET status = 'published' WHERE id = ? AND status = 'pending'");
        
        if ($stmt->execute([$post_id])) {
            $affected_rows = $stmt->rowCount();
            if ($affected_rows > 0) {
                $success = "Post ID {$post_id} has been approved and published successfully!";
            } else {
                $error = "Post not found or already approved.";
            }
        } else {
            $error = "Failed to approve post.";
        }
    } else {
        $error = "Invalid post ID.";
    }
} else {
    $error = "Missing required parameters. Use: /approve.php?post&id=POST_ID";
}

// If accessed directly, show a simple response
if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
    // AJAX request
    header('Content-Type: application/json');
    echo json_encode([
        'success' => !empty($success),
        'message' => $success ?: $error
    ]);
    exit();
}

// Regular request - show simple page
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Post Approval - Cyber Bangla Lab</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #0c1a2b;
            color: #e8f4f8;
            padding: 20px;
            text-align: center;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: #1e3b5d;
            padding: 30px;
            border-radius: 10px;
            border: 2px solid #00c9a7;
        }
        .success {
            color: #00c9a7;
            font-size: 18px;
            margin-bottom: 20px;
        }
        .error {
            color: #ff6b6b;
            font-size: 18px;
            margin-bottom: 20px;
        }
        .info {
            color: #a0b4c0;
            font-size: 14px;
            margin-top: 20px;
        }
        a {
            color: #00c9a7;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>🔐 Post Approval System</h2>
        
        <?php if ($success): ?>
            <div class="success">
                ✅ <?php echo htmlspecialchars($success); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="error">
                ❌ <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>
        
        <div class="info">
            <p><strong>Usage:</strong> /approve.php?post&id=POST_ID</p>
            <p>This endpoint is used to approve pending blog posts.</p>
            <p><a href="index.php">← Back to Home</a></p>
        </div>
    </div>
</body>
</html>
