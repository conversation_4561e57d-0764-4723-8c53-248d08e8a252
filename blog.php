<?php
require_once 'config.php';

$blog_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($blog_id <= 0) {
    header('Location: index.php');
    exit();
}

// Get blog post with author information
$stmt = $pdo->prepare("
    SELECT b.*, u.name as author_name 
    FROM blogs b 
    JOIN users u ON b.author_id = u.id 
    WHERE b.id = ? AND b.status = 'published'
");
$stmt->execute([$blog_id]);
$blog = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$blog) {
    header('Location: index.php');
    exit();
}

$page_title = $blog['title'];
include 'includes/header.php';
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <article class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <span class="badge bg-primary mb-2"><?php echo htmlspecialchars($blog['category']); ?></span>
                            <h1 class="h3 mb-0 text-primary"><?php echo htmlspecialchars($blog['title']); ?></h1>
                        </div>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-user me-1"></i>By <?php echo htmlspecialchars($blog['author_name']); ?>
                            <i class="fas fa-calendar ms-3 me-1"></i><?php echo date('F j, Y', strtotime($blog['created_at'])); ?>
                            <?php if ($blog['updated_at'] !== $blog['created_at']): ?>
                                <i class="fas fa-edit ms-3 me-1"></i>Updated <?php echo date('F j, Y', strtotime($blog['updated_at'])); ?>
                            <?php endif; ?>
                        </small>
                    </div>
                </div>
                <div class="card-body">
                    <div class="blog-content">
                        <?php echo $blog['content']; ?>
                    </div>
                </div>
            </article>

            <div class="mt-4">
                <a href="index.php" class="btn btn-outline-light">
                    <i class="fas fa-arrow-left me-2"></i>Back to Home
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.blog-content {
    line-height: 1.8;
}

.blog-content h3 {
    color: var(--accent-teal);
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.blog-content p {
    margin-bottom: 1.5rem;
}

.blog-content ul {
    margin-bottom: 1.5rem;
}

.blog-content li {
    margin-bottom: 0.5rem;
}
</style>

<?php include 'includes/footer.php'; ?>
